package com.xinjian.framework.web.service;

import cn.hutool.core.util.IdUtil;
import com.xinjian.common.core.domain.dto.LoginUser;
import com.xinjian.common.core.domain.dto.UserSessionInfo;
import com.xinjian.common.exception.status500.ServiceException;
import com.xinjian.common.properties.JwtProperties;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.ExpiredJwtException;
import io.jsonwebtoken.MalformedJwtException;
import io.jsonwebtoken.UnsupportedJwtException;
import io.jsonwebtoken.security.SignatureException;
import java.time.LocalDateTime;
import javax.servlet.http.HttpServletRequest;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

/**
 * 用户会话管理服务
 *
 * <p>负责用户会话的完整生命周期管理，包括：
 * <ul>
 *   <li>用户登录信息的获取和管理</li>
 *   <li>用户会话信息的获取和管理</li>
 *   <li>用户会话的创建、更新和删除</li>
 *   <li>Token的创建和用户信息的缓存</li>
 * </ul>
 *
 * <p>该服务作为用户会话管理的核心，协调JWT令牌服务、缓存管理服务和用户代理解析服务。
 */
@Validated
@Slf4j
@Service
@RequiredArgsConstructor
public class UserSessionManager {

  private final JwtTokenService jwtTokenService;
  private final TokenCacheManager tokenCacheManager;
  private final UserAgentParser userAgentParser;
  private final JwtProperties jwtProperties;

  /**
   * 从HTTP请求中获取并解析Token，返回登录用户信息
   *
   * @param request HTTP请求对象，不能为空
   * @return LoginUser 登录用户信息，如果Token无效或不存在，则返回null
   */
  public LoginUser getLoginUser(@NotNull(message = "HttpServletRequest 不能为空") HttpServletRequest request) {
    try {
      // 获取请求携带的令牌
      String token = jwtTokenService.getTokenFromRequest(request);
      if (StringUtils.isNotEmpty(token)) {
        // 解析对应的权限以及用户信息
        Claims claims = jwtTokenService.parseToken(token);
        String uuid = (String) claims.get(jwtProperties.getLoginUserKey());
        LoginUser loginUser = tokenCacheManager.getLoginUser(uuid).orElse(null);

        if (loginUser == null) {
          log.warn("Token有效但用户信息在缓存中未找到，token: {}", token);
        }

        return loginUser;
      }
    } catch (ExpiredJwtException e) {
      log.warn("Token已过期：{}", e.getMessage());
    } catch (UnsupportedJwtException e) {
      log.warn("Token不受支持：{}", e.getMessage());
    } catch (MalformedJwtException e) {
      log.warn("Token格式错误：{}", e.getMessage());
    } catch (SignatureException e) {
      log.warn("Token签名验证失败：{}", e.getMessage());
    } catch (IllegalArgumentException e) {
      log.warn("Token为空或空字符串：{}", e.getMessage());
    } catch (Exception e) {
      log.error("解析Token时发生错误：{}", e.getMessage(), e);
    }
    return null;
  }

  /**
   * 从HTTP请求中获取并解析Token，返回用户会话信息
   *
   * @param request HTTP请求对象，不能为空
   * @return UserSessionInfo 用户会话信息，如果Token无效或不存在，则返回null
   */
  public UserSessionInfo getUserSessionInfo(@NotNull(message = "HttpServletRequest 不能为空") HttpServletRequest request) {
    try {
      // 获取请求携带的令牌
      String token = jwtTokenService.getTokenFromRequest(request);
      if (StringUtils.isNotEmpty(token)) {
        // 解析对应的权限以及用户信息
        Claims claims = jwtTokenService.parseToken(token);
        String uuid = (String) claims.get(jwtProperties.getLoginUserKey());
        UserSessionInfo sessionInfo = tokenCacheManager.getUserSessionInfo(uuid).orElse(null);

        if (sessionInfo == null) {
          log.warn("Token有效但用户会话信息在缓存中未找到，token: {}", token);
        }

        return sessionInfo;
      }
    } catch (ExpiredJwtException e) {
      log.warn("Token已过期：{}", e.getMessage());
    } catch (UnsupportedJwtException e) {
      log.warn("Token不受支持：{}", e.getMessage());
    } catch (MalformedJwtException e) {
      log.warn("Token格式错误：{}", e.getMessage());
    } catch (SignatureException e) {
      log.warn("Token签名验证失败：{}", e.getMessage());
    } catch (IllegalArgumentException e) {
      log.warn("Token为空或空字符串：{}", e.getMessage());
    } catch (Exception e) {
      log.error("解析Token时发生错误：{}", e.getMessage(), e);
    }
    return null;
  }

  /**
   * 更新登录用户的缓存信息
   *
   * @param tokenUUID Token UUID
   */
  public void setLoginUser(@NotBlank(message = "Token UUID 不能为空") String tokenUUID) {
    try {
      // 刷新缓存有效期
      tokenCacheManager.refreshBothCaches(tokenUUID);
    } catch (Exception e) {
      log.error("设置登录用户时发生错误：{}", e.getMessage(), e);
    }
  }

  /**
   * 更新登录用户的缓存信息
   *
   * @param loginUser 登录用户信息，不能为空
   */
  public void setLoginUser(@NotNull(message = "登录用户信息不能为空") LoginUser loginUser) {
    try {
      String tokenUUID = loginUser.getUsername(); // 这里需要改进，应该从实际的token中获取UUID
      // 更新LoginUser
      tokenCacheManager.refreshLoginUser(tokenUUID, loginUser);

      // 同时刷新会话信息
      tokenCacheManager.getUserSessionInfo(tokenUUID).ifPresent(sessionInfo -> {
        sessionInfo.setExpireTime(LocalDateTime.now().plusMinutes(jwtProperties.getTokenExpireTime()));
        tokenCacheManager.refreshUserSessionInfo(tokenUUID, sessionInfo);
      });
    } catch (Exception e) {
      log.error("设置登录用户时发生错误：{}", e.getMessage(), e);
    }
  }

  /**
   * 从Redis中删除指定Token对应的用户登录信息和会话信息
   *
   * @param token 待删除的用户Token
   */
  public void delLoginUser(@NotBlank(message = "用户Token不能为空") String token) {
    try {
      // 从token中提取UUID
      String uuid = jwtTokenService.extractUuidFromToken(token);
      if (uuid != null) {
        tokenCacheManager.deleteAllCaches(uuid);
        log.info("成功删除登录用户和会话信息，token: {}", token);
      } else {
        log.warn("无法从token中提取UUID，删除操作失败");
      }
    } catch (Exception e) {
      log.error("删除登录用户时发生错误，token {}: {}", token, e.getMessage(), e);
    }
  }

  /**
   * 为指定登录用户创建一个新的Token
   *
   * @param loginUser 登录用户信息，不能为空
   * @return 生成的Token字符串
   * @throws RuntimeException 如果创建Token失败
   */
  public String createToken(@NotNull(message = "登录用户信息不能为空") LoginUser loginUser) throws RuntimeException {
    try {
      // 缓存登录用户并获取token UUID
      String tokenUUID = cacheLoginUser(loginUser);

      // 创建JWT令牌
      String jwtToken = jwtTokenService.createToken(tokenUUID);

      log.info("成功为用户创建token，用户名：{}", loginUser.getUsername());
      return jwtToken;
    } catch (Exception e) {
      log.error("为用户创建token时发生错误，用户名 {}: {}", loginUser.getUsername(), e.getMessage(), e);
      throw new ServiceException("创建token失败：" + e.getMessage());
    }
  }

  /**
   * 验证Token的有效期
   *
   * @param loginUser 登录用户信息，不能为空
   */
  public void verifyToken(@NotNull(message = "登录用户信息不能为空") LoginUser loginUser) {
    try {
      // 从token中获取uuid
      String tokenUUID = loginUser.getUsername(); // 这里暂时使用username作为key，实际应该从token中解析

      // 刷新缓存
      tokenCacheManager.refreshBothCaches(tokenUUID);

      log.debug("成功刷新用户token，用户名：{}", loginUser.getUsername());
    } catch (Exception e) {
      log.error("验证用户token时发生错误，用户名 {}: {}", loginUser.getUsername(), e.getMessage(), e);
    }
  }

  /**
   * 刷新Token的有效期，并将其重新存入Redis缓存
   *
   * @param loginUser 登录用户信息，不能为空
   * @param sessionInfo 用户会话信息，不能为空
   */
  public void refreshToken(
      @NotNull(message = "登录用户信息不能为空") LoginUser loginUser,
      @NotNull(message = "用户会话信息不能为空") UserSessionInfo sessionInfo) {
    try {
      String tokenUUID = sessionInfo.getToken();

      // 刷新LoginUser缓存
      tokenCacheManager.refreshLoginUser(tokenUUID, loginUser);

      // 刷新UserSessionInfo缓存
      tokenCacheManager.refreshUserSessionInfo(tokenUUID, sessionInfo);

      log.debug("成功刷新用户token，用户名：{}", loginUser.getUsername());
    } catch (Exception e) {
      log.error("刷新用户token时发生错误，用户名 {}: {}", loginUser.getUsername(), e.getMessage(), e);
      throw new ServiceException("刷新token失败：" + e.getMessage());
    }
  }

  /**
   * 缓存登录用户信息，包括设置用户代理和刷新Token
   *
   * @param loginUser 登录用户信息，不能为空
   * @return 生成的令牌UUID字符串
   */
  private String cacheLoginUser(@NotNull(message = "登录用户信息不能为空") LoginUser loginUser) {
    // 创建UserSessionInfo对象来管理会话信息
    UserSessionInfo sessionInfo = createUserSessionInfo(loginUser);
    String tokenUUID = generateTokenUuid();
    sessionInfo.setToken(tokenUUID);

    // 设置用户代理信息
    userAgentParser.parseAndSetUserAgent(sessionInfo);

    // 同时存储LoginUser和UserSessionInfo
    tokenCacheManager.storeLoginUserAndSession(tokenUUID, loginUser, sessionInfo);
    return tokenUUID;
  }

  /**
   * 创建用户会话信息对象
   *
   * @param loginUser 登录用户信息
   * @return UserSessionInfo对象
   */
  private UserSessionInfo createUserSessionInfo(@NotNull(message = "登录用户信息不能为空") LoginUser loginUser) {
    UserSessionInfo sessionInfo = new UserSessionInfo();
    sessionInfo.setUserId(loginUser.getUserId());
    sessionInfo.setUsername(loginUser.getUsername());
    sessionInfo.setDeptId(loginUser.getDeptId());
    return sessionInfo;
  }

  /**
   * 生成令牌UUID
   *
   * @return 生成的令牌UUID字符串
   */
  private String generateTokenUuid() {
    return IdUtil.fastUUID();
  }
}
