package com.xinjian.framework.web.service;

import cn.hutool.core.util.IdUtil;
import com.xinjian.common.constant.CacheConstants;
import com.xinjian.common.core.domain.dto.LoginUser;
import com.xinjian.common.core.domain.dto.UserSessionInfo;
import com.xinjian.common.exception.status500.ServiceException;
import com.xinjian.common.properties.JwtProperties;
import com.xinjian.common.service.AddressService;
import com.xinjian.common.utils.JwtTokenUtils;
import com.xinjian.common.utils.ServletUtils;
import com.xinjian.common.utils.ip.IpUtils;
import com.xinjian.starter.redis.core.RedisClient;
import eu.bitwalker.useragentutils.UserAgent;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.ExpiredJwtException;
import io.jsonwebtoken.MalformedJwtException;
import io.jsonwebtoken.UnsupportedJwtException;
import io.jsonwebtoken.security.SignatureException;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import javax.servlet.http.HttpServletRequest;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;

/**
 * Token 服务，负责令牌的生成、解析、刷新和验证
 *
 * <p>该服务封装了对 JWT（JSON Web Token）的操作，并结合 Redis 缓存来管理用户登录状态主要功能包括：
 *
 * <ul>
 *   <li>为登录用户创建 Token
 *   <li>从请求中解析 Token 并获取用户信息
 *   <li>自动刷新即将过期的 Token
 *   <li>验证 Token 的有效性
 *   <li>删除用户登录信息
 * </ul>
 */
@Validated
@Slf4j
@Component
@RequiredArgsConstructor
public class JwtTokenManager {

  /** 毫秒 */
  protected static final long MILLIS_SECOND = 1000;

  /** 分钟 */
  protected static final long MILLIS_MINUTE = 60 * MILLIS_SECOND;

  /** 令牌刷新时间阈值，单位为毫秒当令牌剩余有效期小于此值时，将触发刷新机制当前设置为 20 分钟 */
  private static final Long TOKEN_REFRESH_THRESHOLD_MILLIS = 20 * 60 * 1000L;

  private final RedisClient redisClient;
  private final JwtProperties authProperties;
  private final AddressService addressService;

  /**
   * 从 HTTP 请求中获取并解析 Token，返回登录用户信息
   *
   * @param request HTTP 请求对象，不能为空
   * @return {@link LoginUser} 登录用户信息如果 Token 无效或不存在，则返回 {@code null}
   */
  public LoginUser getLoginUser(
      @NotNull(message = "HttpServletRequest 不能为空") HttpServletRequest request) {
    try {
      // 获取请求携带的令牌
      String token = getToken(request);
      if (StringUtils.isNotEmpty(token)) {
        // 解析对应的权限以及用户信息
        Claims claims = JwtTokenUtils.parseToken(token, authProperties.getTokenSecret());
        String uuid = (String) claims.get(authProperties.getLoginUserKey());
        String userKey = getTokenKey(uuid);
        LoginUser loginUser = redisClient.get(userKey, LoginUser.class).orElse(null);

        if (loginUser == null) {
          log.warn("Token 有效但用户信息在缓存中未找到，token: {}", token);
        }

        return loginUser;
      }
    } catch (ExpiredJwtException e) {
      log.warn("Token 已过期：{}", e.getMessage());
    } catch (UnsupportedJwtException e) {
      log.warn("Token 不受支持：{}", e.getMessage());
    } catch (MalformedJwtException e) {
      log.warn("Token 格式错误：{}", e.getMessage());
    } catch (SignatureException e) {
      log.warn("Token 签名验证失败：{}", e.getMessage());
    } catch (IllegalArgumentException e) {
      log.warn("Token 为空或空字符串：{}", e.getMessage());
    } catch (Exception e) {
      log.error("解析 Token 时发生错误：{}", e.getMessage(), e);
    }
    return null;
  }

  /**
   * 从 HTTP 请求中获取并解析 Token，返回用户会话信息
   *
   * @param request HTTP 请求对象，不能为空
   * @return {@link UserSessionInfo} 用户会话信息如果 Token 无效或不存在，则返回 {@code null}
   */
  public UserSessionInfo getUserSessionInfo(
      @NotNull(message = "HttpServletRequest 不能为空") HttpServletRequest request) {
    try {
      // 获取请求携带的令牌
      String token = getToken(request);
      if (StringUtils.isNotEmpty(token)) {
        // 解析对应的权限以及用户信息
        Claims claims = JwtTokenUtils.parseToken(token, authProperties.getTokenSecret());
        String uuid = (String) claims.get(authProperties.getLoginUserKey());
        String sessionKey = getSessionKey(uuid);
        UserSessionInfo sessionInfo =
            redisClient.get(sessionKey, UserSessionInfo.class).orElse(null);

        if (sessionInfo == null) {
          log.warn("Token 有效但用户会话信息在缓存中未找到，token: {}", token);
        }

        return sessionInfo;
      }
    } catch (ExpiredJwtException e) {
      log.warn("Token 已过期：{}", e.getMessage());
    } catch (UnsupportedJwtException e) {
      log.warn("Token 不受支持：{}", e.getMessage());
    } catch (MalformedJwtException e) {
      log.warn("Token 格式错误：{}", e.getMessage());
    } catch (SignatureException e) {
      log.warn("Token 签名验证失败：{}", e.getMessage());
    } catch (IllegalArgumentException e) {
      log.warn("Token 为空或空字符串：{}", e.getMessage());
    } catch (Exception e) {
      log.error("解析 Token 时发生错误：{}", e.getMessage(), e);
    }
    return null;
  }

  /**
   * 更新登录用户的缓存信息
   *
   * @param tokenUUID Token UUID
   */
  public void setLoginUser(@NotBlank(message = "Token UUID 不能为空") String tokenUUID) {
    try {
      // 刷新缓存有效期
      String userKey = getTokenKey(tokenUUID);
      LoginUser loginUser = redisClient.get(userKey, LoginUser.class).orElse(null);
      if (loginUser != null) {
        redisClient.set(userKey, loginUser, authProperties.getTokenExpireTime(), TimeUnit.MINUTES);

        // 同时刷新会话信息
        String sessionKey = getSessionKey(tokenUUID);
        UserSessionInfo sessionInfo =
            redisClient.get(sessionKey, UserSessionInfo.class).orElse(null);
        if (sessionInfo != null) {
          sessionInfo.setExpireTime(
              LocalDateTime.now().plusMinutes(authProperties.getTokenExpireTime()));
          redisClient.set(
              sessionKey, sessionInfo, authProperties.getTokenExpireTime(), TimeUnit.MINUTES);
        }
      }
    } catch (Exception e) {
      log.error("设置登录用户时发生错误：{}", e.getMessage(), e);
    }
  }

  /**
   * 更新登录用户的缓存信息
   *
   * @param loginUser 登录用户信息，不能为空
   */
  public void setLoginUser(@NotNull(message = "登录用户信息不能为空") LoginUser loginUser) {
    try {
      String tokenUUID = loginUser.getUsername();
      // 更新 LoginUser
      String userKey = getTokenKey(tokenUUID);
      redisClient.set(userKey, loginUser, authProperties.getTokenExpireTime(), TimeUnit.MINUTES);

      // 同时刷新会话信息
      String sessionKey = getSessionKey(tokenUUID);
      UserSessionInfo sessionInfo = redisClient.get(sessionKey, UserSessionInfo.class).orElse(null);
      if (sessionInfo != null) {
        sessionInfo.setExpireTime(
            LocalDateTime.now().plusMinutes(authProperties.getTokenExpireTime()));
        redisClient.set(
            sessionKey, sessionInfo, authProperties.getTokenExpireTime(), TimeUnit.MINUTES);
      }
    } catch (Exception e) {
      log.error("设置登录用户时发生错误：{}", e.getMessage(), e);
    }
  }

  /**
   * 从 Redis 中删除指定 Token 对应的用户登录信息和会话信息
   *
   * @param token 待删除的用户 Token
   */
  public void delLoginUser(@NotBlank(message = "用户 Token 不能为空") String token) {
    try {
      String userKey = getTokenKey(token);
      String sessionKey = getSessionKey(token);

      // 同时删除 LoginUser 和 UserSessionInfo
      redisClient.delete(userKey);
      redisClient.delete(sessionKey);

      log.info("成功删除登录用户和会话信息，token: {}", token);
    } catch (Exception e) {
      log.error("删除登录用户时发生错误，token {}: {}", token, e.getMessage(), e);
    }
  }

  /**
   * 为指定登录用户创建一个新的 Token
   *
   * @param loginUser 登录用户信息，不能为空
   * @return 生成的 Token 字符串
   * @throws RuntimeException 如果创建 Token 失败
   */
  public String createToken(@NotNull(message = "登录用户信息不能为空") LoginUser loginUser)
      throws RuntimeException {
    try {
      // 缓存登录用户并获取 token UUID
      String tokenUUID = cacheLoginUser(loginUser);

      // 构建令牌声明
      Map<String, Object> claims = buildTokenClaims(tokenUUID);

      // 创建 JWT 令牌
      String jwtToken = createJwtToken(claims, authProperties.getTokenSecret());

      log.info("成功为用户创建 token，用户名：{}", loginUser.getUsername());
      return jwtToken;
    } catch (Exception e) {
      log.error("为用户创建 token 时发生错误，用户名 {}: {}", loginUser.getUsername(), e.getMessage(), e);
      throw new ServiceException("创建 token 失败：" + e.getMessage());
    }
  }

  /**
   * 生成令牌 UUID 并设置到登录用户信息中
   *
   * @param loginUser 登录用户信息，不能为空
   * @return 生成的令牌 UUID 字符串
   */
  private String generateTokenUuid(@NotNull(message = "登录用户信息不能为空") LoginUser loginUser) {
    return IdUtil.fastUUID();
  }

  /**
   * 缓存登录用户信息，包括设置用户代理和刷新 Token
   *
   * @param loginUser 登录用户信息，不能为空
   */
  private String cacheLoginUser(@NotNull(message = "登录用户信息不能为空") LoginUser loginUser) {
    // 创建 UserSessionInfo 对象来管理会话信息
    UserSessionInfo sessionInfo = createUserSessionInfo(loginUser);
    String tokenUUID = generateTokenUuid(loginUser);
    sessionInfo.setToken(tokenUUID);

    // 设置用户代理信息
    setUserAgent(sessionInfo);

    // 同时存储 LoginUser 和 UserSessionInfo
    storeLoginUserAndSession(tokenUUID, loginUser, sessionInfo);
    return tokenUUID;
  }

  /**
   * 存储 LoginUser 和 UserSessionInfo 到 Redis
   *
   * @param tokenUUID Token UUID
   * @param loginUser 登录用户信息
   * @param sessionInfo 用户会话信息
   */
  private void storeLoginUserAndSession(
      @NotBlank(message = "Token UUID 不能为空") String tokenUUID,
      @NotNull(message = "登录用户信息不能为空") LoginUser loginUser,
      @NotNull(message = "用户会话信息不能为空") UserSessionInfo sessionInfo) {
    try {
      LocalDateTime now = LocalDateTime.now();
      sessionInfo.setLoginTime(now);
      sessionInfo.setExpireTime(now.plusMinutes(authProperties.getTokenExpireTime()));

      // 存储 LoginUser
      String userKey = getTokenKey(tokenUUID);
      redisClient.set(userKey, loginUser, authProperties.getTokenExpireTime(), TimeUnit.MINUTES);

      // 存储 UserSessionInfo (使用不同的 key)
      String sessionKey = getSessionKey(tokenUUID);
      redisClient.set(
          sessionKey, sessionInfo, authProperties.getTokenExpireTime(), TimeUnit.MINUTES);

      log.debug("成功存储用户登录信息和会话信息，用户名：{}", loginUser.getUsername());
    } catch (Exception e) {
      log.error("存储用户登录信息和会话信息时发生错误，用户名 {}: {}", loginUser.getUsername(), e.getMessage(), e);
      throw new ServiceException("存储用户信息失败：" + e.getMessage());
    }
  }

  /**
   * 创建用户会话信息对象
   *
   * @param loginUser 登录用户信息
   * @return UserSessionInfo 对象
   */
  private UserSessionInfo createUserSessionInfo(
      @NotNull(message = "登录用户信息不能为空") LoginUser loginUser) {
    UserSessionInfo sessionInfo = new UserSessionInfo();
    sessionInfo.setUserId(loginUser.getUserId());
    sessionInfo.setUsername(loginUser.getUsername());
    sessionInfo.setDeptId(loginUser.getDeptId());

    return sessionInfo;
  }

  /**
   * 构建 JWT 令牌的声明信息
   *
   * @param token 令牌 UUID
   * @return 包含声明信息的 Map
   */
  private Map<String, Object> buildTokenClaims(String token) {
    Map<String, Object> claims = new HashMap<>();
    claims.put(authProperties.getLoginUserKey(), token);
    return claims;
  }

  /**
   * 创建 JWT 令牌
   *
   * @param claims 令牌声明信息
   * @param secret 令牌密钥
   * @return 生成的 JWT 令牌字符串
   */
  private String createJwtToken(Map<String, Object> claims, String secret) {
    return JwtTokenUtils.createToken(claims, secret);
  }

  /**
   * 验证 Token 的有效期
   *
   * @param loginUser 登录用户信息，不能为空
   */
  public void verifyToken(@NotNull(message = "登录用户信息不能为空") LoginUser loginUser) {
    try {
      // 从 token 中获取 uuid
      String tokenUUID = loginUser.getUsername(); // 这里暂时使用 username 作为 key，实际应该从 token 中解析

      // 刷新 LoginUser 的过期时间
      String userKey = getTokenKey(tokenUUID);
      redisClient.set(userKey, loginUser, authProperties.getTokenExpireTime(), TimeUnit.MINUTES);

      // 同时刷新 UserSessionInfo 的过期时间
      String sessionKey = getSessionKey(tokenUUID);
      UserSessionInfo sessionInfo = redisClient.get(sessionKey, UserSessionInfo.class).orElse(null);
      if (sessionInfo != null) {
        sessionInfo.setExpireTime(
            LocalDateTime.now().plusMinutes(authProperties.getTokenExpireTime()));
        redisClient.set(
            sessionKey, sessionInfo, authProperties.getTokenExpireTime(), TimeUnit.MINUTES);
      }

      log.debug("成功刷新用户 token，用户名：{}", loginUser.getUsername());
    } catch (Exception e) {
      log.error("验证用户 token 时发生错误，用户名 {}: {}", loginUser.getUsername(), e.getMessage(), e);
    }
  }

  /**
   * 刷新 Token 的有效期，并将其重新存入 Redis 缓存
   *
   * @param loginUser 登录用户信息，不能为空
   * @param sessionInfo 用户会话信息，不能为空
   */
  public void refreshToken(
      @NotNull(message = "登录用户信息不能为空") LoginUser loginUser,
      @NotNull(message = "用户会话信息不能为空") UserSessionInfo sessionInfo) {
    try {
      LocalDateTime now = LocalDateTime.now();
      sessionInfo.setLoginTime(now);
      sessionInfo.setExpireTime(now.plusMinutes(authProperties.getTokenExpireTime()));

      // 根据 token 将 sessionInfo 缓存
      String userKey = getTokenKey(sessionInfo.getToken());
      redisClient.set(userKey, sessionInfo, authProperties.getTokenExpireTime(), TimeUnit.MINUTES);

      log.debug("成功刷新用户 token，用户名：{}", loginUser.getUsername());
    } catch (Exception e) {
      log.error("刷新用户 token 时发生错误，用户名 {}: {}", loginUser.getUsername(), e.getMessage(), e);
      throw new ServiceException("刷新 token 失败：" + e.getMessage());
    }
  }

  /**
   * 设置用户代理信息，包括 IP 地址、地理位置、浏览器和操作系统
   *
   * @param sessionInfo 用户会话信息，不能为空
   */
  public void setUserAgent(@NotNull(message = "用户会话信息不能为空") UserSessionInfo sessionInfo) {
    try {
      final HttpServletRequest request = ServletUtils.getRequest();
      if (request != null) {
        parseUserAgentDetails(sessionInfo, request);
      } else {
        log.warn("无法获取 HttpServletRequest，无法设置用户代理详情");
      }
    } catch (Exception e) {
      log.error("设置用户代理时发生错误，用户名 {}: {}", sessionInfo.getUsername(), e.getMessage(), e);
    }
  }

  /**
   * 从 HttpServletRequest 中解析 User-Agent 和 IP 地址，并设置到 UserSessionInfo 对象中
   *
   * @param sessionInfo 用户会话信息
   * @param request HTTP 请求
   */
  private void parseUserAgentDetails(UserSessionInfo sessionInfo, HttpServletRequest request) {
    try {
      final UserAgent userAgent = UserAgent.parseUserAgentString(request.getHeader("User-Agent"));
      final String ip = IpUtils.getIpAddr(request);
      sessionInfo.setIpaddr(ip);

      try {
        String location = addressService.getRealAddressByIP(ip);
        sessionInfo.setLoginLocation(location);
      } catch (Exception e) {
        log.warn("根据 IP 获取位置失败 {}: {}", ip, e.getMessage());
        sessionInfo.setLoginLocation("未知");
      }

      sessionInfo.setBrowser(userAgent.getBrowser().getName());
      sessionInfo.setOs(userAgent.getOperatingSystem().getName());

      log.debug(
          "成功设置用户代理详情，用户名：{}, IP: {}, 位置：{}",
          sessionInfo.getUsername(),
          ip,
          sessionInfo.getLoginLocation());
    } catch (Exception e) {
      log.error("解析用户代理详情时发生错误：{}", e.getMessage(), e);
      // 设置默认值，避免空指针
      sessionInfo.setIpaddr("未知");
      sessionInfo.setLoginLocation("未知");
      sessionInfo.setBrowser("未知");
      sessionInfo.setOs("未知");
    }
  }

  /**
   * 从 HTTP 请求头中获取 Token
   *
   * @param request HTTP 请求
   * @return Token 字符串，去除了 "Bearer " 前缀
   */
  private String getToken(HttpServletRequest request) {
    try {
      String token = request.getHeader(authProperties.getTokenHeader());
      if (StringUtils.isNotEmpty(token) && token.startsWith(authProperties.getTokenPrefix())) {
        // 使用 substring 移除 "Bearer " 前缀，避免 replace 可能导致的空格问题
        token = token.substring(authProperties.getTokenPrefix().length());
        // 去除前后空格，确保 Token 格式正确
        token = token.trim();
      }
      return token;
    } catch (Exception e) {
      log.error("从请求中获取 token 时发生错误：{}", e.getMessage(), e);
      return null;
    }
  }

  /**
   * 根据 UUID 生成 Redis 缓存的 Key
   *
   * @param uuid 唯一标识符
   * @return Redis Key 字符串
   */
  private String getTokenKey(String uuid) {
    return CacheConstants.LOGIN_TOKEN_KEY + ':' + uuid;
  }

  /**
   * 根据 UUID 生成 Redis 会话缓存的 Key
   *
   * @param uuid 唯一标识符
   * @return Redis Key 字符串
   */
  private String getSessionKey(String uuid) {
    return CacheConstants.LOGIN_TOKEN_KEY + ":session:" + uuid;
  }
}
