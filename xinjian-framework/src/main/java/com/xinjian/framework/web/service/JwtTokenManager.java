package com.xinjian.framework.web.service;

import com.xinjian.common.core.domain.dto.LoginUser;
import com.xinjian.common.core.domain.dto.UserSessionInfo;
import javax.servlet.http.HttpServletRequest;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;

/**
 * JWT令牌管理器（门面类）
 *
 * <p>负责JWT令牌的完整生命周期管理，包括：
 * <ul>
 *   <li>Token的创建和签发</li>
 *   <li>Token的解析和验证</li>
 *   <li>Token的刷新和续期</li>
 *   <li>用户会话的缓存管理</li>
 *   <li>Token的撤销和清理</li>
 * </ul>
 *
 * <p>该类作为门面模式的实现，内部委托给专门的服务类来处理具体的业务逻辑，
 * 保持了对外API的兼容性，同时提高了代码的可维护性和可测试性。
 */
@Validated
@Slf4j
@Component
@RequiredArgsConstructor
public class JwtTokenManager {

  private final UserSessionManager userSessionManager;
  private final UserAgentParser userAgentParser;

  /**
   * 从 HTTP 请求中获取并解析 Token，返回登录用户信息
   *
   * @param request HTTP 请求对象，不能为空
   * @return {@link LoginUser} 登录用户信息如果 Token 无效或不存在，则返回 {@code null}
   */
  public LoginUser getLoginUser(
      @NotNull(message = "HttpServletRequest 不能为空") HttpServletRequest request) {
    return userSessionManager.getLoginUser(request);
  }

  /**
   * 从 HTTP 请求中获取并解析 Token，返回用户会话信息
   *
   * @param request HTTP 请求对象，不能为空
   * @return {@link UserSessionInfo} 用户会话信息如果 Token 无效或不存在，则返回 {@code null}
   */
  public UserSessionInfo getUserSessionInfo(
      @NotNull(message = "HttpServletRequest 不能为空") HttpServletRequest request) {
    return userSessionManager.getUserSessionInfo(request);
  }

  /**
   * 更新登录用户的缓存信息
   *
   * @param tokenUUID Token UUID
   */
  public void setLoginUser(@NotBlank(message = "Token UUID 不能为空") String tokenUUID) {
    userSessionManager.setLoginUser(tokenUUID);
  }

  /**
   * 更新登录用户的缓存信息
   *
   * @param loginUser 登录用户信息，不能为空
   */
  public void setLoginUser(@NotNull(message = "登录用户信息不能为空") LoginUser loginUser) {
    userSessionManager.setLoginUser(loginUser);
  }

  /**
   * 从 Redis 中删除指定 Token 对应的用户登录信息和会话信息
   *
   * @param token 待删除的用户 Token
   */
  public void delLoginUser(@NotBlank(message = "用户 Token 不能为空") String token) {
    userSessionManager.delLoginUser(token);
  }

  /**
   * 为指定登录用户创建一个新的 Token
   *
   * @param loginUser 登录用户信息，不能为空
   * @return 生成的 Token 字符串
   * @throws RuntimeException 如果创建 Token 失败
   */
  public String createToken(@NotNull(message = "登录用户信息不能为空") LoginUser loginUser)
      throws RuntimeException {
    return userSessionManager.createToken(loginUser);
  }

  /**
   * 验证Token的有效期
   *
   * @param loginUser 登录用户信息，不能为空
   */
  public void verifyToken(@NotNull(message = "登录用户信息不能为空") LoginUser loginUser) {
    userSessionManager.verifyToken(loginUser);
  }

  /**
   * 刷新Token的有效期，并将其重新存入Redis缓存
   *
   * @param loginUser 登录用户信息，不能为空
   * @param sessionInfo 用户会话信息，不能为空
   */
  public void refreshToken(
      @NotNull(message = "登录用户信息不能为空") LoginUser loginUser,
      @NotNull(message = "用户会话信息不能为空") UserSessionInfo sessionInfo) {
    userSessionManager.refreshToken(loginUser, sessionInfo);
  }

  /**
   * 设置用户代理信息，包括IP地址、地理位置、浏览器和操作系统
   *
   * @param sessionInfo 用户会话信息，不能为空
   */
  public void setUserAgent(@NotNull(message = "用户会话信息不能为空") UserSessionInfo sessionInfo) {
    userAgentParser.parseAndSetUserAgent(sessionInfo);
  }
}
