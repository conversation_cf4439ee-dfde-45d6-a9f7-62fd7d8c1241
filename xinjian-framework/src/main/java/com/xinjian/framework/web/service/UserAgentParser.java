package com.xinjian.framework.web.service;

import com.xinjian.common.core.domain.dto.UserSessionInfo;
import com.xinjian.common.service.AddressService;
import com.xinjian.common.utils.ServletUtils;
import com.xinjian.common.utils.ip.IpUtils;
import eu.bitwalker.useragentutils.UserAgent;
import javax.servlet.http.HttpServletRequest;
import javax.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

/**
 * 用户代理信息解析服务
 *
 * <p>负责解析用户的环境信息，包括：
 * <ul>
 *   <li>IP地址的获取和解析</li>
 *   <li>地理位置信息的获取</li>
 *   <li>浏览器信息的解析</li>
 *   <li>操作系统信息的解析</li>
 * </ul>
 *
 * <p>该服务专注于用户环境信息的解析，不涉及会话管理和缓存操作。
 */
@Validated
@Slf4j
@Service
@RequiredArgsConstructor
public class UserAgentParser {

  private final AddressService addressService;

  /**
   * 解析并设置用户代理信息
   *
   * <p>从当前HTTP请求中解析用户代理信息，包括IP地址、地理位置、浏览器和操作系统，
   * 并将这些信息设置到UserSessionInfo对象中。
   *
   * @param sessionInfo 用户会话信息对象，不能为空
   */
  public void parseAndSetUserAgent(@NotNull(message = "用户会话信息不能为空") UserSessionInfo sessionInfo) {
    try {
      final HttpServletRequest request = ServletUtils.getRequest();
      if (request != null) {
        parseUserAgentDetails(sessionInfo, request);
      } else {
        log.warn("无法获取HttpServletRequest，无法设置用户代理详情");
        setDefaultUserAgentInfo(sessionInfo);
      }
    } catch (Exception e) {
      log.error("设置用户代理时发生错误，用户名 {}: {}", sessionInfo.getUsername(), e.getMessage(), e);
      setDefaultUserAgentInfo(sessionInfo);
    }
  }

  /**
   * 解析并设置用户代理信息（指定请求对象）
   *
   * @param sessionInfo 用户会话信息对象，不能为空
   * @param request HTTP请求对象，不能为空
   */
  public void parseAndSetUserAgent(
      @NotNull(message = "用户会话信息不能为空") UserSessionInfo sessionInfo,
      @NotNull(message = "HttpServletRequest 不能为空") HttpServletRequest request) {
    try {
      parseUserAgentDetails(sessionInfo, request);
    } catch (Exception e) {
      log.error("设置用户代理时发生错误，用户名 {}: {}", sessionInfo.getUsername(), e.getMessage(), e);
      setDefaultUserAgentInfo(sessionInfo);
    }
  }

  /**
   * 从HttpServletRequest中解析User-Agent和IP地址，并设置到UserSessionInfo对象中
   *
   * @param sessionInfo 用户会话信息
   * @param request HTTP请求
   */
  private void parseUserAgentDetails(UserSessionInfo sessionInfo, HttpServletRequest request) {
    try {
      // 解析User-Agent信息
      final UserAgent userAgent = UserAgent.parseUserAgentString(request.getHeader("User-Agent"));
      
      // 获取IP地址
      final String ip = IpUtils.getIpAddr(request);
      sessionInfo.setIpaddr(ip);

      // 获取地理位置信息
      try {
        String location = addressService.getRealAddressByIP(ip);
        sessionInfo.setLoginLocation(location);
      } catch (Exception e) {
        log.warn("根据IP获取位置失败 {}: {}", ip, e.getMessage());
        sessionInfo.setLoginLocation("未知");
      }

      // 设置浏览器和操作系统信息
      sessionInfo.setBrowser(userAgent.getBrowser().getName());
      sessionInfo.setOs(userAgent.getOperatingSystem().getName());

      log.debug(
          "成功设置用户代理详情，用户名：{}, IP: {}, 位置：{}, 浏览器：{}, 操作系统：{}",
          sessionInfo.getUsername(),
          ip,
          sessionInfo.getLoginLocation(),
          sessionInfo.getBrowser(),
          sessionInfo.getOs());
    } catch (Exception e) {
      log.error("解析用户代理详情时发生错误：{}", e.getMessage(), e);
      setDefaultUserAgentInfo(sessionInfo);
    }
  }

  /**
   * 设置默认的用户代理信息
   *
   * @param sessionInfo 用户会话信息
   */
  private void setDefaultUserAgentInfo(UserSessionInfo sessionInfo) {
    sessionInfo.setIpaddr("未知");
    sessionInfo.setLoginLocation("未知");
    sessionInfo.setBrowser("未知");
    sessionInfo.setOs("未知");
  }

  /**
   * 仅解析IP地址信息
   *
   * @param request HTTP请求对象
   * @return IP地址字符串，如果解析失败返回"未知"
   */
  public String parseIpAddress(@NotNull(message = "HttpServletRequest 不能为空") HttpServletRequest request) {
    try {
      return IpUtils.getIpAddr(request);
    } catch (Exception e) {
      log.error("解析IP地址时发生错误：{}", e.getMessage(), e);
      return "未知";
    }
  }

  /**
   * 仅解析地理位置信息
   *
   * @param ip IP地址
   * @return 地理位置字符串，如果解析失败返回"未知"
   */
  public String parseLocation(@NotNull(message = "IP地址不能为空") String ip) {
    try {
      return addressService.getRealAddressByIP(ip);
    } catch (Exception e) {
      log.warn("根据IP获取位置失败 {}: {}", ip, e.getMessage());
      return "未知";
    }
  }

  /**
   * 仅解析浏览器信息
   *
   * @param userAgentString User-Agent字符串
   * @return 浏览器名称，如果解析失败返回"未知"
   */
  public String parseBrowser(String userAgentString) {
    try {
      if (userAgentString == null || userAgentString.trim().isEmpty()) {
        return "未知";
      }
      final UserAgent userAgent = UserAgent.parseUserAgentString(userAgentString);
      return userAgent.getBrowser().getName();
    } catch (Exception e) {
      log.error("解析浏览器信息时发生错误：{}", e.getMessage(), e);
      return "未知";
    }
  }

  /**
   * 仅解析操作系统信息
   *
   * @param userAgentString User-Agent字符串
   * @return 操作系统名称，如果解析失败返回"未知"
   */
  public String parseOperatingSystem(String userAgentString) {
    try {
      if (userAgentString == null || userAgentString.trim().isEmpty()) {
        return "未知";
      }
      final UserAgent userAgent = UserAgent.parseUserAgentString(userAgentString);
      return userAgent.getOperatingSystem().getName();
    } catch (Exception e) {
      log.error("解析操作系统信息时发生错误：{}", e.getMessage(), e);
      return "未知";
    }
  }
}
