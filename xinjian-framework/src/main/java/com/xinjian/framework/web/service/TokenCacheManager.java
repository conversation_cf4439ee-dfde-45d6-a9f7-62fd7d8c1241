package com.xinjian.framework.web.service;

import com.xinjian.common.constant.CacheConstants;
import com.xinjian.common.core.domain.dto.LoginUser;
import com.xinjian.common.core.domain.dto.UserSessionInfo;
import com.xinjian.common.properties.JwtProperties;
import com.xinjian.starter.redis.core.RedisClient;
import java.time.LocalDateTime;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

/**
 * Token缓存管理服务
 *
 * <p>负责Token相关的缓存操作，包括：
 * <ul>
 *   <li>LoginUser信息的缓存管理</li>
 *   <li>UserSessionInfo信息的缓存管理</li>
 *   <li>缓存的存储、获取、删除和刷新操作</li>
 *   <li>缓存Key的生成和管理</li>
 * </ul>
 *
 * <p>该服务专注于缓存操作，不涉及JWT令牌的解析和用户代理信息的处理。
 */
@Validated
@Slf4j
@Service
@RequiredArgsConstructor
public class TokenCacheManager {

  private final RedisClient redisClient;
  private final JwtProperties jwtProperties;

  /**
   * 存储LoginUser到缓存
   *
   * @param tokenUUID Token UUID
   * @param loginUser 登录用户信息
   */
  public void storeLoginUser(
      @NotBlank(message = "Token UUID 不能为空") String tokenUUID,
      @NotNull(message = "登录用户信息不能为空") LoginUser loginUser) {
    try {
      String userKey = getTokenKey(tokenUUID);
      redisClient.set(userKey, loginUser, jwtProperties.getTokenExpireTime(), TimeUnit.MINUTES);
      log.debug("成功存储LoginUser到缓存，用户名：{}", loginUser.getUsername());
    } catch (Exception e) {
      log.error("存储LoginUser到缓存时发生错误，用户名 {}: {}", loginUser.getUsername(), e.getMessage(), e);
      throw new RuntimeException("存储LoginUser失败：" + e.getMessage(), e);
    }
  }

  /**
   * 存储UserSessionInfo到缓存
   *
   * @param tokenUUID Token UUID
   * @param sessionInfo 用户会话信息
   */
  public void storeUserSessionInfo(
      @NotBlank(message = "Token UUID 不能为空") String tokenUUID,
      @NotNull(message = "用户会话信息不能为空") UserSessionInfo sessionInfo) {
    try {
      LocalDateTime now = LocalDateTime.now();
      sessionInfo.setLoginTime(now);
      sessionInfo.setExpireTime(now.plusMinutes(jwtProperties.getTokenExpireTime()));

      String sessionKey = getSessionKey(tokenUUID);
      redisClient.set(sessionKey, sessionInfo, jwtProperties.getTokenExpireTime(), TimeUnit.MINUTES);
      log.debug("成功存储UserSessionInfo到缓存，用户名：{}", sessionInfo.getUsername());
    } catch (Exception e) {
      log.error("存储UserSessionInfo到缓存时发生错误，用户名 {}: {}", sessionInfo.getUsername(), e.getMessage(), e);
      throw new RuntimeException("存储UserSessionInfo失败：" + e.getMessage(), e);
    }
  }

  /**
   * 同时存储LoginUser和UserSessionInfo到缓存
   *
   * @param tokenUUID Token UUID
   * @param loginUser 登录用户信息
   * @param sessionInfo 用户会话信息
   */
  public void storeLoginUserAndSession(
      @NotBlank(message = "Token UUID 不能为空") String tokenUUID,
      @NotNull(message = "登录用户信息不能为空") LoginUser loginUser,
      @NotNull(message = "用户会话信息不能为空") UserSessionInfo sessionInfo) {
    try {
      // 存储LoginUser
      storeLoginUser(tokenUUID, loginUser);
      
      // 存储UserSessionInfo
      storeUserSessionInfo(tokenUUID, sessionInfo);
      
      log.debug("成功存储用户登录信息和会话信息，用户名：{}", loginUser.getUsername());
    } catch (Exception e) {
      log.error("存储用户登录信息和会话信息时发生错误，用户名 {}: {}", loginUser.getUsername(), e.getMessage(), e);
      throw new RuntimeException("存储用户信息失败：" + e.getMessage(), e);
    }
  }

  /**
   * 从缓存中获取LoginUser
   *
   * @param tokenUUID Token UUID
   * @return LoginUser对象的Optional包装，如果不存在则返回空Optional
   */
  public Optional<LoginUser> getLoginUser(@NotBlank(message = "Token UUID 不能为空") String tokenUUID) {
    try {
      String userKey = getTokenKey(tokenUUID);
      return redisClient.get(userKey, LoginUser.class);
    } catch (Exception e) {
      log.error("从缓存获取LoginUser时发生错误，UUID {}: {}", tokenUUID, e.getMessage(), e);
      return Optional.empty();
    }
  }

  /**
   * 从缓存中获取UserSessionInfo
   *
   * @param tokenUUID Token UUID
   * @return UserSessionInfo对象的Optional包装，如果不存在则返回空Optional
   */
  public Optional<UserSessionInfo> getUserSessionInfo(@NotBlank(message = "Token UUID 不能为空") String tokenUUID) {
    try {
      String sessionKey = getSessionKey(tokenUUID);
      return redisClient.get(sessionKey, UserSessionInfo.class);
    } catch (Exception e) {
      log.error("从缓存获取UserSessionInfo时发生错误，UUID {}: {}", tokenUUID, e.getMessage(), e);
      return Optional.empty();
    }
  }

  /**
   * 刷新LoginUser的缓存过期时间
   *
   * @param tokenUUID Token UUID
   * @param loginUser 登录用户信息
   */
  public void refreshLoginUser(
      @NotBlank(message = "Token UUID 不能为空") String tokenUUID,
      @NotNull(message = "登录用户信息不能为空") LoginUser loginUser) {
    try {
      String userKey = getTokenKey(tokenUUID);
      redisClient.set(userKey, loginUser, jwtProperties.getTokenExpireTime(), TimeUnit.MINUTES);
      log.debug("成功刷新LoginUser缓存，用户名：{}", loginUser.getUsername());
    } catch (Exception e) {
      log.error("刷新LoginUser缓存时发生错误，用户名 {}: {}", loginUser.getUsername(), e.getMessage(), e);
    }
  }

  /**
   * 刷新UserSessionInfo的缓存过期时间
   *
   * @param tokenUUID Token UUID
   * @param sessionInfo 用户会话信息
   */
  public void refreshUserSessionInfo(
      @NotBlank(message = "Token UUID 不能为空") String tokenUUID,
      @NotNull(message = "用户会话信息不能为空") UserSessionInfo sessionInfo) {
    try {
      sessionInfo.setExpireTime(LocalDateTime.now().plusMinutes(jwtProperties.getTokenExpireTime()));
      String sessionKey = getSessionKey(tokenUUID);
      redisClient.set(sessionKey, sessionInfo, jwtProperties.getTokenExpireTime(), TimeUnit.MINUTES);
      log.debug("成功刷新UserSessionInfo缓存，用户名：{}", sessionInfo.getUsername());
    } catch (Exception e) {
      log.error("刷新UserSessionInfo缓存时发生错误，用户名 {}: {}", sessionInfo.getUsername(), e.getMessage(), e);
    }
  }

  /**
   * 同时刷新LoginUser和UserSessionInfo的缓存过期时间
   *
   * @param tokenUUID Token UUID
   */
  public void refreshBothCaches(@NotBlank(message = "Token UUID 不能为空") String tokenUUID) {
    try {
      // 获取并刷新LoginUser
      Optional<LoginUser> loginUserOpt = getLoginUser(tokenUUID);
      if (loginUserOpt.isPresent()) {
        refreshLoginUser(tokenUUID, loginUserOpt.get());
      }

      // 获取并刷新UserSessionInfo
      Optional<UserSessionInfo> sessionInfoOpt = getUserSessionInfo(tokenUUID);
      if (sessionInfoOpt.isPresent()) {
        refreshUserSessionInfo(tokenUUID, sessionInfoOpt.get());
      }

      log.debug("成功刷新用户缓存，UUID：{}", tokenUUID);
    } catch (Exception e) {
      log.error("刷新用户缓存时发生错误，UUID {}: {}", tokenUUID, e.getMessage(), e);
    }
  }

  /**
   * 删除指定Token对应的所有缓存信息
   *
   * @param tokenUUID Token UUID
   */
  public void deleteAllCaches(@NotBlank(message = "Token UUID 不能为空") String tokenUUID) {
    try {
      String userKey = getTokenKey(tokenUUID);
      String sessionKey = getSessionKey(tokenUUID);

      // 同时删除LoginUser和UserSessionInfo
      redisClient.delete(userKey);
      redisClient.delete(sessionKey);

      log.info("成功删除登录用户和会话信息缓存，UUID: {}", tokenUUID);
    } catch (Exception e) {
      log.error("删除用户缓存时发生错误，UUID {}: {}", tokenUUID, e.getMessage(), e);
    }
  }

  /**
   * 根据UUID生成Redis缓存的Key
   *
   * @param uuid 唯一标识符
   * @return Redis Key字符串
   */
  private String getTokenKey(String uuid) {
    return CacheConstants.LOGIN_TOKEN_KEY + ':' + uuid;
  }

  /**
   * 根据UUID生成Redis会话缓存的Key
   *
   * @param uuid 唯一标识符
   * @return Redis Key字符串
   */
  private String getSessionKey(String uuid) {
    return CacheConstants.LOGIN_TOKEN_KEY + ":session:" + uuid;
  }
}
