package com.xinjian.framework.web.service;

import com.xinjian.common.core.domain.dto.LoginUser;
import com.xinjian.common.core.domain.dto.UserSessionInfo;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.mock.web.MockHttpServletRequest;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * JwtTokenManager 重构验证测试
 *
 * <p>验证重构后的门面类是否正确委托给各个专门的服务类
 */
@ExtendWith(MockitoExtension.class)
class JwtTokenManagerRefactoringTest {

    @Mock
    private UserSessionManager userSessionManager;

    @Mock
    private UserAgentParser userAgentParser;

    @InjectMocks
    private JwtTokenManager jwtTokenManager;

    @Test
    void testGetLoginUser_ShouldDelegateToUserSessionManager() {
        // Given
        MockHttpServletRequest request = new MockHttpServletRequest();
        LoginUser expectedUser = new LoginUser();
        when(userSessionManager.getLoginUser(request)).thenReturn(expectedUser);

        // When
        LoginUser result = jwtTokenManager.getLoginUser(request);

        // Then
        assertEquals(expectedUser, result);
        verify(userSessionManager).getLoginUser(request);
    }

    @Test
    void testGetUserSessionInfo_ShouldDelegateToUserSessionManager() {
        // Given
        MockHttpServletRequest request = new MockHttpServletRequest();
        UserSessionInfo expectedSessionInfo = new UserSessionInfo();
        when(userSessionManager.getUserSessionInfo(request)).thenReturn(expectedSessionInfo);

        // When
        UserSessionInfo result = jwtTokenManager.getUserSessionInfo(request);

        // Then
        assertEquals(expectedSessionInfo, result);
        verify(userSessionManager).getUserSessionInfo(request);
    }

    @Test
    void testSetLoginUserWithUUID_ShouldDelegateToUserSessionManager() {
        // Given
        String tokenUUID = "test-uuid";

        // When
        jwtTokenManager.setLoginUser(tokenUUID);

        // Then
        verify(userSessionManager).setLoginUser(tokenUUID);
    }

    @Test
    void testSetLoginUserWithLoginUser_ShouldDelegateToUserSessionManager() {
        // Given
        LoginUser loginUser = new LoginUser();

        // When
        jwtTokenManager.setLoginUser(loginUser);

        // Then
        verify(userSessionManager).setLoginUser(loginUser);
    }

    @Test
    void testDelLoginUser_ShouldDelegateToUserSessionManager() {
        // Given
        String token = "test-token";

        // When
        jwtTokenManager.delLoginUser(token);

        // Then
        verify(userSessionManager).delLoginUser(token);
    }

    @Test
    void testCreateToken_ShouldDelegateToUserSessionManager() {
        // Given
        LoginUser loginUser = new LoginUser();
        String expectedToken = "generated-token";
        when(userSessionManager.createToken(loginUser)).thenReturn(expectedToken);

        // When
        String result = jwtTokenManager.createToken(loginUser);

        // Then
        assertEquals(expectedToken, result);
        verify(userSessionManager).createToken(loginUser);
    }

    @Test
    void testVerifyToken_ShouldDelegateToUserSessionManager() {
        // Given
        LoginUser loginUser = new LoginUser();

        // When
        jwtTokenManager.verifyToken(loginUser);

        // Then
        verify(userSessionManager).verifyToken(loginUser);
    }

    @Test
    void testRefreshToken_ShouldDelegateToUserSessionManager() {
        // Given
        LoginUser loginUser = new LoginUser();
        UserSessionInfo sessionInfo = new UserSessionInfo();

        // When
        jwtTokenManager.refreshToken(loginUser, sessionInfo);

        // Then
        verify(userSessionManager).refreshToken(loginUser, sessionInfo);
    }

    @Test
    void testSetUserAgent_ShouldDelegateToUserAgentParser() {
        // Given
        UserSessionInfo sessionInfo = new UserSessionInfo();

        // When
        jwtTokenManager.setUserAgent(sessionInfo);

        // Then
        verify(userAgentParser).parseAndSetUserAgent(sessionInfo);
    }

    @Test
    void testAllMethodsAreProperlyDelegated() {
        // 验证所有公共方法都有对应的委托调用
        // 这个测试确保门面类没有遗漏任何方法的委托

        // Given
        MockHttpServletRequest request = new MockHttpServletRequest();
        LoginUser loginUser = new LoginUser();
        UserSessionInfo sessionInfo = new UserSessionInfo();
        String token = "test-token";
        String tokenUUID = "test-uuid";

        when(userSessionManager.getLoginUser(any())).thenReturn(loginUser);
        when(userSessionManager.getUserSessionInfo(any())).thenReturn(sessionInfo);
        when(userSessionManager.createToken(any())).thenReturn(token);

        // When - 调用所有公共方法
        jwtTokenManager.getLoginUser(request);
        jwtTokenManager.getUserSessionInfo(request);
        jwtTokenManager.setLoginUser(tokenUUID);
        jwtTokenManager.setLoginUser(loginUser);
        jwtTokenManager.delLoginUser(token);
        jwtTokenManager.createToken(loginUser);
        jwtTokenManager.verifyToken(loginUser);
        jwtTokenManager.refreshToken(loginUser, sessionInfo);
        jwtTokenManager.setUserAgent(sessionInfo);

        // Then - 验证所有委托调用都发生了
        verify(userSessionManager).getLoginUser(request);
        verify(userSessionManager).getUserSessionInfo(request);
        verify(userSessionManager).setLoginUser(tokenUUID);
        verify(userSessionManager).setLoginUser(loginUser);
        verify(userSessionManager).delLoginUser(token);
        verify(userSessionManager).createToken(loginUser);
        verify(userSessionManager).verifyToken(loginUser);
        verify(userSessionManager).refreshToken(loginUser, sessionInfo);
        verify(userAgentParser).parseAndSetUserAgent(sessionInfo);
    }
}
