package com.xinjian.common.utils;

import io.jsonwebtoken.Claims;
import io.jsonwebtoken.JwtParser;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.security.Keys;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import javax.crypto.SecretKey;

/** Jwt 工具类 */
public class JwtTokenUtils {

  private static final Map<String, JwtParser> JWT_PARSERS = new ConcurrentHashMap<>();

  /**
   * 从数据声明生成令牌
   *
   * @param claims 数据声明
   * @param secret 秘钥
   * @return 令牌
   */
  public static String createToken(Map<String, Object> claims, String secret) {
    SecretKey secretKey = Keys.hmacShaKeyFor(secret.getBytes(StandardCharsets.UTF_8));
    return Jwts.builder().claims(claims).signWith(secretKey).compact();
  }

  /**
   * 从令牌中获取数据声明
   *
   * @param token 令牌
   * @param secret 秘钥
   * @return 数据声明
   */
  public static Claims parseToken(String token, String secret) {
    JwtParser parser =
        JWT_PARSERS.computeIfAbsent(
            secret,
            k -> {
              SecretKey secretKey = Keys.hmacShaKeyFor(k.getBytes(StandardCharsets.UTF_8));
              return Jwts.parser().verifyWith(secretKey).build();
            });
    return parser.parseSignedClaims(token).getPayload();
  }

  /**
   * 判断令牌是否过期
   *
   * @param token 令牌
   * @return 是否过期
   */
  public static boolean isTokenExpired(String token, String secret) {
    try {
      return parseToken(token, secret).getExpiration().before(new Date());
    } catch (Exception e) {
      return true;
    }
  }
}
