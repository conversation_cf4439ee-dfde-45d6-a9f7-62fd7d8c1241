package com.xinjian.common.config;

import com.xinjian.common.properties.*;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/** 应用配置属性自动配置类 */
@Configuration
@EnableConfigurationProperties({
  AppProperties.class,
  ApiProperties.class,
  StorageProperties.class,
  JwtProperties.class,
  UserProperties.class,
  XssProperties.class,
  PaginationProperties.class
})
public class ApplicationPropertiesConfig {}
