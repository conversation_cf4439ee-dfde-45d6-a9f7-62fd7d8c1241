package com.xinjian.framework.web.service;

import com.xinjian.common.properties.JwtProperties;
import com.xinjian.common.utils.JwtTokenUtils;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.ExpiredJwtException;
import io.jsonwebtoken.MalformedJwtException;
import io.jsonwebtoken.UnsupportedJwtException;
import io.jsonwebtoken.security.SignatureException;
import java.util.HashMap;
import java.util.Map;
import javax.servlet.http.HttpServletRequest;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

/**
 * JWT令牌服务
 *
 * <p>负责JWT令牌的完整生命周期管理，包括：
 * <ul>
 *   <li>Token的创建和签发</li>
 *   <li>Token的解析和验证</li>
 *   <li>从HTTP请求中提取Token</li>
 * </ul>
 *
 * <p>该服务专注于JWT令牌本身的操作，不涉及用户会话管理和缓存操作。
 */
@Validated
@Slf4j
@Service
@RequiredArgsConstructor
public class JwtTokenService {

  private final JwtProperties jwtProperties;

  /**
   * 创建JWT令牌
   *
   * @param tokenUUID 令牌UUID，用作令牌的唯一标识
   * @return 生成的JWT令牌字符串
   * @throws RuntimeException 如果创建令牌失败
   */
  public String createToken(@NotBlank(message = "Token UUID 不能为空") String tokenUUID) {
    try {
      // 构建令牌声明
      Map<String, Object> claims = buildTokenClaims(tokenUUID);
      
      // 创建JWT令牌
      String jwtToken = JwtTokenUtils.createToken(claims, jwtProperties.getTokenSecret());
      
      log.debug("成功创建JWT令牌，UUID: {}", tokenUUID);
      return jwtToken;
    } catch (Exception e) {
      log.error("创建JWT令牌失败，UUID {}: {}", tokenUUID, e.getMessage(), e);
      throw new RuntimeException("创建JWT令牌失败：" + e.getMessage(), e);
    }
  }

  /**
   * 解析JWT令牌并获取Claims
   *
   * @param token JWT令牌字符串
   * @return Claims对象，包含令牌中的声明信息
   * @throws ExpiredJwtException 令牌已过期
   * @throws UnsupportedJwtException 令牌不受支持
   * @throws MalformedJwtException 令牌格式错误
   * @throws SignatureException 令牌签名验证失败
   * @throws IllegalArgumentException 令牌为空或空字符串
   */
  public Claims parseToken(@NotBlank(message = "JWT令牌不能为空") String token) {
    return JwtTokenUtils.parseToken(token, jwtProperties.getTokenSecret());
  }

  /**
   * 从JWT令牌中提取UUID
   *
   * @param token JWT令牌字符串
   * @return 令牌中的UUID，如果解析失败则返回null
   */
  public String extractUuidFromToken(@NotBlank(message = "JWT令牌不能为空") String token) {
    try {
      Claims claims = parseToken(token);
      return (String) claims.get(jwtProperties.getLoginUserKey());
    } catch (Exception e) {
      log.warn("从JWT令牌中提取UUID失败：{}", e.getMessage());
      return null;
    }
  }

  /**
   * 从HTTP请求中获取JWT令牌
   *
   * @param request HTTP请求对象
   * @return JWT令牌字符串，去除了Bearer前缀；如果未找到或格式错误则返回null
   */
  public String getTokenFromRequest(@NotNull(message = "HttpServletRequest 不能为空") HttpServletRequest request) {
    try {
      String token = request.getHeader(jwtProperties.getTokenHeader());
      if (StringUtils.isNotEmpty(token) && token.startsWith(jwtProperties.getTokenPrefix())) {
        // 使用substring移除"Bearer "前缀，避免replace可能导致的空格问题
        token = token.substring(jwtProperties.getTokenPrefix().length());
        // 去除前后空格，确保Token格式正确
        token = token.trim();
        return StringUtils.isNotEmpty(token) ? token : null;
      }
      return null;
    } catch (Exception e) {
      log.error("从请求中获取JWT令牌时发生错误：{}", e.getMessage(), e);
      return null;
    }
  }

  /**
   * 验证JWT令牌的有效性
   *
   * @param token JWT令牌字符串
   * @return true表示令牌有效，false表示令牌无效
   */
  public boolean validateToken(@NotBlank(message = "JWT令牌不能为空") String token) {
    try {
      parseToken(token);
      return true;
    } catch (ExpiredJwtException e) {
      log.debug("JWT令牌已过期：{}", e.getMessage());
    } catch (UnsupportedJwtException e) {
      log.debug("JWT令牌不受支持：{}", e.getMessage());
    } catch (MalformedJwtException e) {
      log.debug("JWT令牌格式错误：{}", e.getMessage());
    } catch (SignatureException e) {
      log.debug("JWT令牌签名验证失败：{}", e.getMessage());
    } catch (IllegalArgumentException e) {
      log.debug("JWT令牌为空或空字符串：{}", e.getMessage());
    } catch (Exception e) {
      log.warn("验证JWT令牌时发生未知错误：{}", e.getMessage(), e);
    }
    return false;
  }

  /**
   * 构建JWT令牌的声明信息
   *
   * @param tokenUUID 令牌UUID
   * @return 包含声明信息的Map
   */
  private Map<String, Object> buildTokenClaims(String tokenUUID) {
    Map<String, Object> claims = new HashMap<>();
    claims.put(jwtProperties.getLoginUserKey(), tokenUUID);
    return claims;
  }
}
