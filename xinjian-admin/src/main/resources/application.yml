# 应用配置
app:
  # 应用标识符
  namespace: demo
  # 版本
  version: 1.0.0
  # 版权年份
  copyright-year: 2025
  # 获取 IP 地址开关
  address-enabled: false

  # API 相关配置
  api:
    base-url: /api/${app.namespace}

  # 文件存储相关配置
  storage:
    # 文件存储物理路径
    path: ./storage
    # 文件访问端点
    endpoint: /api/${app.namespace}/files

  # 认证配置
  jwt:
    # 令牌自定义标识
    token-header: Authorization
    # 令牌前缀
    token-prefix: Bearer
    # 登录用户 redis key
    login-user-key: login_user_key
    # 令牌密钥，注意每个项目都不能一样！使用命令生成 openssl rand -base64 64
    token-secret: V2Vha0tleUV4Y2VwdGlvblRlc3RTZWNyZXRLZXlGb3JIUzUxMkFsZ29yaXRobQ==
    # 令牌有效期（默认 1 day）
    token-expire-time: 1440

  # 用户配置
  user:
    password:
      # 密码最大错误次数
      max-retry-count: 5
      # 密码锁定时间（默认 10 分钟）
      lock-time: 10

  # 验证码配置
  captcha:
    # 是否开启验证码
    enabled: true
    # 可重试次数
    retry-count: 1
    # 验证码类型 math 数字计算 char 字符验证
    type: math
    # 过期时间（分钟）
    expire-time: 2

  # XSS 防护配置
  xss:
    enabled: false
    excludes: /system/notice
    url-patterns: /system/*,/monitor/*,/tool/*

  # 分页配置
  pagination:
    default-page-num: 1
    default-page-size: 10
    max-page-size: 100

xinjian:
  # Redis 配置
  redis:
    enabled: true
    key-prefix: "${app.namespace}:"
    default-ttl: 0 # @Cacheable 注解的默认不过期
  # 验证码配置
  captcha:
    enabled: true
    type: math
    expire-time: 2
    provider: kaptcha
    store: redis # redis 需要自己实现一个 ICaptchaStore

server:
  port: 8123
  servlet:
    context-path: /
  tomcat:
    uri-encoding: UTF-8
    relaxed-query-chars: "[,]"
    accept-count: 1000 # 连接数满后的排队数
    threads:
      max: 800 # Tomcat 最大线程数
      min-spare: 100 # Tomcat 启动初始化的线程数
  error:
    whitelabel:
      enabled: false
    include-exception: true
    include-message: ALWAYS
    include-stacktrace: ON_PARAM

# Spring 配置
spring:
  application:
    name: xinjian-admin-api
  messages:
    basename: i18n/messages
  servlet:
    multipart:
      max-file-size: 10MB # 单个文件大小
      max-request-size: 20MB # 设置总上传的文件大小
  devtools:
    restart:
      enabled: false
  datasource:
    # driver-class-name: com.mysql.cj.jdbc.Driver   # MySQL 驱动
    driver-class-name: org.mariadb.jdbc.Driver # MariaDB 驱动
    dynamic:
      primary: master # 设置主数据源
      hikari:
        # --- 连接池大小 ---
        minimum-idle: 10 # 设置为固定大小，避免连接池动态伸缩的开销
        maximum-pool-size: 10
        # --- 超时与生命周期管理 ---
        connection-timeout: 30000 # 获取连接的超时时间: 30s
        idle-timeout: 600000 # 连接在池中闲置的最长时间: 10min
        max-lifetime: 1800000 # 连接的最长生命周期: 30min。
        # --- 传递给底层 JDBC 驱动的优化参数 ---
        data-source-properties:
          # ---- 关键性能优化 ----
          cachePrepStmts: true # 开启 PreparedStatement 缓存
          prepStmtCacheSize: 250 # PreparedStatement 缓存大小
          prepStmtCacheSqlLimit: 2048 # 缓存的 SQL 最大长度
          useServerPrepStmts: true # 使用服务端 PreparedStatement
          useLocalSessionState: true # 允许驱动使用会话级别的状态信息
          rewriteBatchedStatements: true # 开启批量更新/插入的优化
          cacheResultSetMetadata: true # 缓存结果集的元数据
          cacheServerConfiguration: true # 缓存服务端的配置信息
          elideSetAutoCommits: true # 优化 `setAutoCommit()` 的调用
          maintainTimeStats: false # 关闭时间统计，减少性能开销
          # ---- MariaDB 特有优化 ----
          usePipelineAuth: true
          useBatchMultiSend: true
          useSessionVariables: true
  jackson:
    serialization:
      write-dates-as-timestamps: false
  redis:
    timeout: 3000ms
    lettuce:
      pool:
        min-idle: 0 # 连接池中的最小空闲连接
        max-idle: 8 # 连接池中的最大空闲连接
        max-active: 8 # 连接池的最大数据库连接数
        max-wait: -1ms # 连接池最大阻塞等待时间，使用负值表示没有限制

# 日志配置
logging:
  level:
    com.xinjian: debug
    org.springframework: warn

# MyBatis Plus 配置
mybatis-plus:
  # 搜索指定包别名
  type-aliases-package: com.xinjian.**.domain,com.xinjian.**.entity
  # 配置 mapper 的扫描，找到所有的 mapper.xml 映射文件
  mapper-locations: classpath*:mapper/**/*Mapper.xml
  # MyBatis Plus 原生配置
  configuration:
    map-underscore-to-camel-case: true
  global-config:
    db-config:
      logic-delete-field: is_deleted # 全局逻辑删除字段名
      logic-delete-value: 1 # 逻辑已删除值。可选，默认值为 1
      logic-not-delete-value: 0 # 逻辑未删除值。可选，默认值为 0

# Swagger 配置
springdoc:
  packages-to-scan:
    - com.xinjian.admin.web
    - com.xinjian.quartz.controller
    - com.xinjian.generator.controller
  show-login-endpoint: true
  swagger-ui:
    path: /swagger-ui
    tags-sorter: alpha
    operations-sorter: alpha
    persist-authorization: true
